import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { emailService } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { prisma } = await import('@/lib/prisma')
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { admin: true }
    })

    if (!user?.admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const body = await request.json()
    const { testType, recipient } = body

    switch (testType) {
      case 'configuration':
        const configTest = await emailService.testConfiguration()
        return NextResponse.json({
          success: true,
          test: 'configuration',
          result: configTest
        })

      case 'send':
        if (!recipient) {
          return NextResponse.json({ error: 'Recipient email required for send test' }, { status: 400 })
        }

        const sendResult = await emailService.sendEmail({
          to: recipient,
          subject: 'AveHub Email Test',
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
              <h1>Email Test Successful! ✅</h1>
              <p>This is a test email from AveHub Developer Portal.</p>
              <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
              <p><strong>Sent to:</strong> ${recipient}</p>
              <p>If you received this email, your email configuration is working correctly.</p>
            </div>
          `,
          text: `Email Test Successful!\n\nThis is a test email from AveHub Developer Portal.\nTimestamp: ${new Date().toISOString()}\nSent to: ${recipient}\n\nIf you received this email, your email configuration is working correctly.`
        })

        return NextResponse.json({
          success: true,
          test: 'send',
          result: { sent: sendResult }
        })

      default:
        return NextResponse.json({ error: 'Invalid test type' }, { status: 400 })
    }
  } catch (error) {
    console.error('Email test error:', error)
    return NextResponse.json({
      error: 'Email test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { prisma } = await import('@/lib/prisma')
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { admin: true }
    })

    if (!user?.admin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get email configuration status
    const configTest = await emailService.testConfiguration()
    
    // Get recent email logs
    const recentEmails = await prisma.emailLog.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: { name: true, email: true }
        }
      }
    })

    // Get email statistics
    const stats = await prisma.emailLog.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    })

    return NextResponse.json({
      configuration: configTest,
      recentEmails,
      statistics: stats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.status
        return acc
      }, {} as Record<string, number>)
    })
  } catch (error) {
    console.error('Email status error:', error)
    return NextResponse.json({
      error: 'Failed to get email status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
