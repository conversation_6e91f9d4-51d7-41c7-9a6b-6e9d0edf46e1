#!/usr/bin/env node

/**
 * MSI Installer Build Script
 * 
 * This script builds the AveHub Desktop application and creates MSI installers
 * for Windows distribution.
 */

const { execSync, spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`)
}

function logStep(step, message) {
  log(`\n${colors.bright}[${step}]${colors.reset} ${colors.cyan}${message}${colors.reset}`)
}

function logSuccess(message) {
  log(`${colors.green}✅ ${message}${colors.reset}`)
}

function logError(message) {
  log(`${colors.red}❌ ${message}${colors.reset}`)
}

function logWarning(message) {
  log(`${colors.yellow}⚠️  ${message}${colors.reset}`)
}

async function checkPrerequisites() {
  logStep('1', 'Checking prerequisites...')
  
  try {
    // Check Node.js version
    const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim()
    log(`Node.js version: ${nodeVersion}`)
    
    // Check if we're on Windows (for MSI building)
    if (process.platform !== 'win32') {
      logWarning('MSI installers can only be built on Windows')
      logWarning('Cross-platform building may not work correctly')
    }
    
    // Check if electron-builder is available
    try {
      execSync('npx electron-builder --version', { encoding: 'utf8', stdio: 'pipe' })
      logSuccess('electron-builder is available')
    } catch (error) {
      throw new Error('electron-builder not found. Run: npm install')
    }
    
    // Check if Next.js project is ready
    if (!fs.existsSync('package.json')) {
      throw new Error('package.json not found. Run this script from the project root.')
    }
    
    if (!fs.existsSync('electron/main.js')) {
      throw new Error('Electron main.js not found. Ensure Electron setup is complete.')
    }
    
    logSuccess('All prerequisites met')
    
  } catch (error) {
    logError(`Prerequisites check failed: ${error.message}`)
    process.exit(1)
  }
}

async function buildNextApp() {
  logStep('2', 'Building Next.js application...')
  
  try {
    log('Running: pnpm exec prisma generate')
    execSync('pnpm exec prisma generate', { stdio: 'inherit' })
    
    log('Running: next build')
    execSync('next build', { stdio: 'inherit' })
    
    logSuccess('Next.js build completed')
    
  } catch (error) {
    logError(`Next.js build failed: ${error.message}`)
    process.exit(1)
  }
}

async function installElectronDependencies() {
  logStep('3', 'Installing Electron dependencies...')
  
  try {
    process.chdir('electron')
    
    log('Running: npm install')
    execSync('npm install', { stdio: 'inherit' })
    
    process.chdir('..')
    logSuccess('Electron dependencies installed')
    
  } catch (error) {
    logError(`Electron dependencies installation failed: ${error.message}`)
    process.exit(1)
  }
}

async function buildMSI() {
  logStep('4', 'Building MSI installer...')
  
  try {
    log('Running: electron-builder --win --config electron/package.json')
    execSync('npx electron-builder --win --config electron/package.json', { 
      stdio: 'inherit',
      cwd: process.cwd()
    })
    
    logSuccess('MSI installer built successfully')
    
    // List built files
    const distDir = path.join(process.cwd(), 'electron', 'dist')
    if (fs.existsSync(distDir)) {
      log('\nBuilt files:')
      const files = fs.readdirSync(distDir)
      files.forEach(file => {
        const filePath = path.join(distDir, file)
        const stats = fs.statSync(filePath)
        const size = (stats.size / 1024 / 1024).toFixed(2)
        log(`  📦 ${file} (${size} MB)`)
      })
    }
    
  } catch (error) {
    logError(`MSI build failed: ${error.message}`)
    process.exit(1)
  }
}

async function validateBuild() {
  logStep('5', 'Validating build...')
  
  try {
    const distDir = path.join(process.cwd(), 'electron', 'dist')
    
    if (!fs.existsSync(distDir)) {
      throw new Error('Distribution directory not found')
    }
    
    const files = fs.readdirSync(distDir)
    const msiFiles = files.filter(file => file.endsWith('.msi'))
    const exeFiles = files.filter(file => file.endsWith('.exe'))
    
    if (msiFiles.length === 0) {
      logWarning('No MSI files found in distribution')
    } else {
      logSuccess(`Found ${msiFiles.length} MSI installer(s)`)
      msiFiles.forEach(file => log(`  📦 ${file}`))
    }
    
    if (exeFiles.length > 0) {
      logSuccess(`Found ${exeFiles.length} EXE installer(s)`)
      exeFiles.forEach(file => log(`  📦 ${file}`))
    }
    
    logSuccess('Build validation completed')
    
  } catch (error) {
    logError(`Build validation failed: ${error.message}`)
    process.exit(1)
  }
}

async function main() {
  log(`${colors.bright}${colors.magenta}🚀 AveHub MSI Installer Build Script${colors.reset}`)
  log(`${colors.bright}Building desktop application with MSI installer...${colors.reset}`)
  
  const startTime = Date.now()
  
  try {
    await checkPrerequisites()
    await buildNextApp()
    await installElectronDependencies()
    await buildMSI()
    await validateBuild()
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(2)
    
    log(`\n${colors.bright}${colors.green}🎉 Build completed successfully in ${duration}s!${colors.reset}`)
    log(`${colors.bright}MSI installer is ready for distribution.${colors.reset}`)
    
  } catch (error) {
    logError(`Build process failed: ${error.message}`)
    process.exit(1)
  }
}

// Handle script arguments
const args = process.argv.slice(2)
if (args.includes('--help') || args.includes('-h')) {
  log(`${colors.bright}AveHub MSI Installer Build Script${colors.reset}`)
  log('')
  log('Usage: node scripts/build-msi.js [options]')
  log('')
  log('Options:')
  log('  --help, -h    Show this help message')
  log('')
  log('This script will:')
  log('1. Check prerequisites')
  log('2. Build the Next.js application')
  log('3. Install Electron dependencies')
  log('4. Build MSI installer using electron-builder')
  log('5. Validate the build output')
  log('')
  log('Requirements:')
  log('- Windows OS (for MSI building)')
  log('- Node.js v18+')
  log('- Visual Studio Build Tools')
  log('')
  process.exit(0)
}

// Run the main function
main().catch(error => {
  logError(`Unexpected error: ${error.message}`)
  process.exit(1)
})
