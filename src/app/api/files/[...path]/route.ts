import { NextRequest, NextResponse } from 'next/server'
import { storageService } from '@/lib/storage'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params
    const filePath = path.join('/')
    
    // Extract the key from the path
    // URL format: /api/files/apps/{appId}/{fileType}/{filename}
    if (path.length < 3) {
      return NextResponse.json({ error: 'Invalid file path' }, { status: 400 })
    }

    const key = filePath
    
    // Download file from storage
    const fileBuffer = await storageService.downloadFile(key)
    
    if (!fileBuffer) {
      return NextResponse.json({ error: 'File not found' }, { status: 404 })
    }

    // Determine content type based on file extension
    const filename = path[path.length - 1]
    const extension = filename.split('.').pop()?.toLowerCase()
    
    let contentType = 'application/octet-stream'
    
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg'
        break
      case 'png':
        contentType = 'image/png'
        break
      case 'gif':
        contentType = 'image/gif'
        break
      case 'webp':
        contentType = 'image/webp'
        break
      case 'svg':
        contentType = 'image/svg+xml'
        break
      case 'zip':
        contentType = 'application/zip'
        break
      case 'exe':
        contentType = 'application/x-msdownload'
        break
      case 'dmg':
        contentType = 'application/x-apple-diskimage'
        break
      case 'apk':
        contentType = 'application/vnd.android.package-archive'
        break
      case 'ipa':
        contentType = 'application/octet-stream'
        break
      case 'deb':
        contentType = 'application/vnd.debian.binary-package'
        break
      case 'rpm':
        contentType = 'application/x-rpm'
        break
      case 'msi':
        contentType = 'application/x-msi'
        break
      case 'appimage':
        contentType = 'application/x-executable'
        break
      default:
        contentType = 'application/octet-stream'
    }

    // Set cache headers for static files
    const headers: Record<string, string> = {
      'Content-Type': contentType,
      'Content-Length': fileBuffer.length.toString(),
      'Cache-Control': 'public, max-age=31536000, immutable', // Cache for 1 year
      'ETag': `"${Buffer.from(key).toString('base64')}"`,
    }

    // For images, add additional headers
    if (contentType.startsWith('image/')) {
      headers['Accept-Ranges'] = 'bytes'
    }

    // For MSI files, add security headers and force download
    if (extension === 'msi') {
      headers['Content-Disposition'] = `attachment; filename="${filename}"`
      headers['X-Content-Type-Options'] = 'nosniff'
      headers['X-Download-Options'] = 'noopen'
      headers['Cache-Control'] = 'no-cache, no-store, must-revalidate' // Don't cache executables
    }

    return new NextResponse(fileBuffer, { headers })

  } catch (error) {
    console.error('Error serving file:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
