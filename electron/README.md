# AveHub Desktop Application

This directory contains the Electron wrapper for the AveHub Developer Portal, enabling it to run as a native desktop application on Windows, macOS, and Linux.

## Features

- **Native Desktop Experience**: Run AveHub as a native desktop application
- **Auto-Updates**: Automatic application updates via electron-updater
- **Cross-Platform**: Support for Windows (MSI/NSIS), macOS (DMG), and Linux (AppImage/DEB)
- **Security**: Secure configuration with context isolation and disabled node integration

## Prerequisites

Before building the desktop application, ensure you have:

1. **Node.js** (v18 or higher)
2. **npm** or **pnpm**
3. **Platform-specific build tools**:
   - **Windows**: Visual Studio Build Tools or Visual Studio Community
   - **macOS**: Xcode Command Line Tools
   - **Linux**: Standard build tools (gcc, make, etc.)

## Installation

1. Install dependencies:
   ```bash
   cd electron
   npm install
   ```

2. Install main project dependencies:
   ```bash
   cd ..
   pnpm install
   ```

## Development

To run the desktop application in development mode:

```bash
# From the root directory
npm run electron:dev
```

This will:
1. Start the Next.js development server
2. Wait for it to be ready
3. Launch Electron pointing to the local server

## Building

### Build for Current Platform

```bash
npm run dist
```

### Build for Specific Platforms

```bash
# Windows (creates both NSIS installer and MSI)
npm run dist:win

# macOS (creates DMG)
npm run dist:mac

# Linux (creates AppImage and DEB)
npm run dist:linux
```

## MSI Installer

The Windows build creates both NSIS and MSI installers:

- **NSIS Installer**: `dist/AveHub Developer Portal Setup.exe`
- **MSI Installer**: `dist/AveHub Developer Portal.msi`

### MSI Features

- **Custom Installation Directory**: Users can choose installation location
- **Desktop Shortcut**: Optional desktop shortcut creation
- **Start Menu Integration**: Automatic start menu entry
- **Uninstaller**: Clean uninstallation support
- **Auto-Updates**: Built-in update mechanism

## Configuration

The Electron configuration is defined in `package.json` under the `build` section:

- **App ID**: `io.avehub.desktop`
- **Product Name**: AveHub Developer Portal
- **Publisher**: AveHub
- **Auto-Updates**: Configured for GitHub releases

## Icons

Place your application icons in the `assets/` directory:

- **Windows**: `icon.ico` (256x256 or multiple sizes)
- **macOS**: `icon.icns` (multiple sizes from 16x16 to 1024x1024)
- **Linux**: `icon.png` (512x512 recommended)

## Security

The Electron application is configured with security best practices:

- **Context Isolation**: Enabled
- **Node Integration**: Disabled in renderer
- **Remote Module**: Disabled
- **Web Security**: Enabled
- **External Link Handling**: Opens in default browser

## Auto-Updates

The application supports automatic updates via electron-updater:

1. **Check for Updates**: Automatic on startup
2. **Download**: Background download of updates
3. **Install**: Prompts user to restart and install

Updates are distributed via GitHub releases.

## Troubleshooting

### Build Issues

1. **Missing Dependencies**: Ensure all build tools are installed
2. **Permission Errors**: Run with appropriate permissions
3. **Code Signing**: Disable verification for development builds

### Runtime Issues

1. **Port Conflicts**: Ensure port 3000 is available
2. **Next.js Server**: Check that the Next.js build is successful
3. **Network Issues**: Verify localhost connectivity

## Distribution

1. **GitHub Releases**: Upload built installers to GitHub releases
2. **Auto-Update**: Configure release channels (latest, beta, alpha)
3. **Code Signing**: Sign installers for production distribution

## Support

For issues specific to the desktop application:

1. Check Electron logs in the application data directory
2. Verify Next.js server is running correctly
3. Test the web version first to isolate issues

## License

This desktop wrapper follows the same license as the main AveHub project.
