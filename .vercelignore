# Development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build cache (exclude from deployment to reduce size)
.next/cache
.next/trace

# Development dependencies
node_modules/.cache
node_modules/.pnpm

# Documentation
README.md
docs/
*.md

# Git files
.git/
.gitignore

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Test files
__tests__/
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx

# Development scripts
scripts/
src/scripts/

# Large files that aren't needed for runtime
public/storage/files/*
!public/storage/files/.gitkeep

# TypeScript build info
*.tsbuildinfo

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next/out/

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/
