import type { NextConfig } from "next";
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

const nextConfig: NextConfig = {
  /* config options here */
  serverExternalPackages: ['@prisma/client'],

  // Optimize for Vercel deployment
  experimental: {
    // Reduce bundle size
    optimizePackageImports: ['lucide-react', 'framer-motion'],
  },

  // Configure webpack for better optimization
  webpack: (config, { isServer, dev }) => {
    // Optimize bundle size for production
    if (!dev) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              maxSize: 244000, // 244KB chunks
            },
          },
        },
      }
    }

    return config
  },

  // Increase the maximum request body size to 100MB
  async headers() {
    return []
  },

  // Configure body size limits
  serverRuntimeConfig: {
    maxUploadSize: 100 * 1024 * 1024, // 100MB
  },

  // Output optimization for Vercel
  output: 'standalone',
};

export default withNextIntl(nextConfig);
