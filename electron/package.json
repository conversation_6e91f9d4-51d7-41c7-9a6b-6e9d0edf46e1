{"name": "avehub-desktop", "version": "1.0.0", "description": "AveHub Developer Portal Desktop Application", "main": "main.js", "author": "AveHub Team", "license": "MIT", "homepage": "https://avehub.io", "dependencies": {"electron-is-dev": "^2.0.0", "electron-updater": "^6.1.7"}, "build": {"appId": "io.avehub.desktop", "productName": "AveHub Developer Portal", "directories": {"output": "dist"}, "files": ["main.js", "assets/**/*", "../.next/**/*", "../public/**/*", "../package.json", "../node_modules/**/*"], "extraResources": [{"from": "../", "to": "app", "filter": ["package.json", ".next/**/*", "public/**/*", "node_modules/**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "msi", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "publisherName": "AveHub", "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "AveHub Developer Portal"}, "msi": {"oneClick": false, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "AveHub Developer Portal"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.developer-tools"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Development"}, "publish": {"provider": "github", "owner": "a<PERSON><PERSON>b", "repo": "desktop"}}}