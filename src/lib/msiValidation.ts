/**
 * MSI File Validation and Metadata Extraction
 * 
 * This module provides specialized validation and metadata extraction
 * for Microsoft Installer (MSI) files uploaded to the platform.
 */

export interface MSIMetadata {
  productName?: string
  productVersion?: string
  manufacturer?: string
  productCode?: string
  upgradeCode?: string
  packageCode?: string
  installSize?: number
  targetPlatform?: string
  minimumVersion?: string
  language?: string
  description?: string
  keywords?: string
  comments?: string
  author?: string
  subject?: string
  title?: string
  createdDate?: Date
  modifiedDate?: Date
}

export interface MSIValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  metadata?: MSIMetadata
  securityChecks: {
    hasDigitalSignature: boolean
    signatureValid?: boolean
    certificateInfo?: string
    isTrustedPublisher?: boolean
  }
}

/**
 * Validate MSI file and extract metadata
 */
export async function validateMSIFile(file: File): Promise<MSIValidationResult> {
  const result: MSIValidationResult = {
    isValid: false,
    errors: [],
    warnings: [],
    securityChecks: {
      hasDigitalSignature: false
    }
  }

  try {
    // Basic MSI file structure validation
    const structureValid = await validateMSIStructure(file)
    if (!structureValid) {
      result.errors.push('Invalid MSI file structure')
      return result
    }

    // Extract metadata from MSI
    result.metadata = await extractMSIMetadata(file)

    // Perform security checks
    result.securityChecks = await performMSISecurityChecks(file)

    // Validate metadata completeness
    validateMetadataCompleteness(result.metadata, result)

    // Check for suspicious patterns
    checkSuspiciousPatterns(result.metadata, result)

    // File is valid if no errors
    result.isValid = result.errors.length === 0

    return result

  } catch (error) {
    result.errors.push(`MSI validation error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    return result
  }
}

/**
 * Validate MSI file structure using magic numbers and basic format checks
 */
async function validateMSIStructure(file: File): Promise<boolean> {
  try {
    // Read first 512 bytes to check MSI signature
    const headerBuffer = await file.slice(0, 512).arrayBuffer()
    const headerBytes = new Uint8Array(headerBuffer)
    
    // MSI files are OLE compound documents, check for OLE signature
    const oleSignature = [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1]
    
    // Check if file starts with OLE signature
    for (let i = 0; i < oleSignature.length; i++) {
      if (headerBytes[i] !== oleSignature[i]) {
        return false
      }
    }

    // Additional MSI-specific checks could be added here
    // For now, we rely on the OLE signature which is sufficient for basic validation
    
    return true
  } catch (error) {
    console.error('Error validating MSI structure:', error)
    return false
  }
}

/**
 * Extract metadata from MSI file
 * Note: This is a simplified implementation. Full MSI parsing would require
 * a complete OLE compound document parser.
 */
async function extractMSIMetadata(file: File): Promise<MSIMetadata> {
  const metadata: MSIMetadata = {}

  try {
    // For a complete implementation, we would need to:
    // 1. Parse the OLE compound document structure
    // 2. Extract the MSI database
    // 3. Query the Property table for metadata
    
    // For now, we'll extract basic file information
    metadata.installSize = file.size
    
    // Extract filename-based information
    const filename = file.name
    const nameWithoutExt = filename.replace(/\.msi$/i, '')
    
    // Try to extract version from filename (common patterns)
    const versionMatch = nameWithoutExt.match(/(\d+\.?\d*\.?\d*\.?\d*)/g)
    if (versionMatch && versionMatch.length > 0) {
      metadata.productVersion = versionMatch[versionMatch.length - 1]
    }
    
    // Extract potential product name from filename
    const cleanName = nameWithoutExt
      .replace(/[_-]/g, ' ')
      .replace(/\d+\.?\d*\.?\d*\.?\d*/g, '')
      .replace(/\s+/g, ' ')
      .trim()
    
    if (cleanName) {
      metadata.productName = cleanName
    }

    // Set file dates
    metadata.modifiedDate = new Date(file.lastModified)

    return metadata
  } catch (error) {
    console.error('Error extracting MSI metadata:', error)
    return metadata
  }
}

/**
 * Perform security checks on MSI file
 */
async function performMSISecurityChecks(file: File): Promise<MSIValidationResult['securityChecks']> {
  const securityChecks: MSIValidationResult['securityChecks'] = {
    hasDigitalSignature: false
  }

  try {
    // Check for digital signature
    // MSI files can be digitally signed, but detecting this requires
    // parsing the file structure or using platform-specific APIs
    
    // For now, we'll do a basic check for common signature patterns
    const signatureCheck = await checkForDigitalSignature(file)
    securityChecks.hasDigitalSignature = signatureCheck.hasSignature
    securityChecks.signatureValid = signatureCheck.isValid
    securityChecks.certificateInfo = signatureCheck.certificateInfo

    return securityChecks
  } catch (error) {
    console.error('Error performing MSI security checks:', error)
    return securityChecks
  }
}

/**
 * Basic digital signature detection
 */
async function checkForDigitalSignature(file: File): Promise<{
  hasSignature: boolean
  isValid?: boolean
  certificateInfo?: string
}> {
  try {
    // Read the last 64KB of the file where signatures are typically stored
    const signatureRegion = file.slice(-65536)
    const buffer = await signatureRegion.arrayBuffer()
    const bytes = new Uint8Array(buffer)
    
    // Look for PKCS#7 signature markers
    const pkcs7Markers = [
      [0x30, 0x82], // PKCS#7 SignedData
      [0x30, 0x80], // PKCS#7 indefinite length
    ]
    
    let hasSignature = false
    
    for (const marker of pkcs7Markers) {
      for (let i = 0; i < bytes.length - marker.length; i++) {
        if (bytes[i] === marker[0] && bytes[i + 1] === marker[1]) {
          hasSignature = true
          break
        }
      }
      if (hasSignature) break
    }

    return {
      hasSignature,
      isValid: hasSignature ? undefined : false, // Can't validate without proper crypto APIs
      certificateInfo: hasSignature ? 'Digital signature detected' : undefined
    }
  } catch (error) {
    console.error('Error checking digital signature:', error)
    return { hasSignature: false }
  }
}

/**
 * Validate metadata completeness and add warnings for missing information
 */
function validateMetadataCompleteness(metadata: MSIMetadata | undefined, result: MSIValidationResult): void {
  if (!metadata) {
    result.warnings.push('Could not extract metadata from MSI file')
    return
  }

  const requiredFields = ['productName', 'productVersion']
  const recommendedFields = ['manufacturer', 'description']

  for (const field of requiredFields) {
    if (!metadata[field as keyof MSIMetadata]) {
      result.warnings.push(`Missing required metadata: ${field}`)
    }
  }

  for (const field of recommendedFields) {
    if (!metadata[field as keyof MSIMetadata]) {
      result.warnings.push(`Missing recommended metadata: ${field}`)
    }
  }
}

/**
 * Check for suspicious patterns in MSI metadata
 */
function checkSuspiciousPatterns(metadata: MSIMetadata | undefined, result: MSIValidationResult): void {
  if (!metadata) return

  // Check for suspicious product names
  const suspiciousNames = [
    /virus/i, /malware/i, /trojan/i, /backdoor/i, /keylogger/i,
    /crack/i, /keygen/i, /patch/i, /hack/i
  ]

  const productName = metadata.productName || ''
  const description = metadata.description || ''

  for (const pattern of suspiciousNames) {
    if (pattern.test(productName) || pattern.test(description)) {
      result.warnings.push('Product name or description contains potentially suspicious terms')
      break
    }
  }

  // Check for very large install sizes (potential bloatware)
  if (metadata.installSize && metadata.installSize > 500 * 1024 * 1024) { // 500MB
    result.warnings.push('MSI file is unusually large (>500MB)')
  }

  // Check for missing version information
  if (!metadata.productVersion) {
    result.warnings.push('MSI file lacks version information')
  }
}

/**
 * Get MSI file recommendations based on validation results
 */
export function getMSIRecommendations(validationResult: MSIValidationResult): string[] {
  const recommendations: string[] = []

  if (!validationResult.securityChecks.hasDigitalSignature) {
    recommendations.push('Consider digitally signing your MSI installer for better security and user trust')
  }

  if (validationResult.metadata?.installSize && validationResult.metadata.installSize > 100 * 1024 * 1024) {
    recommendations.push('Large installer size may affect download times - consider compression or splitting into components')
  }

  if (!validationResult.metadata?.description) {
    recommendations.push('Add a description to your MSI package for better user experience')
  }

  if (!validationResult.metadata?.manufacturer) {
    recommendations.push('Include manufacturer information in your MSI package')
  }

  return recommendations
}
